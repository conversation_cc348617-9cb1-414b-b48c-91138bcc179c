import os
import sys
import logging

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 添加Matcha-TTS到Python路径
matcha_path = os.path.join(project_root, 'third_party', 'Matcha-TTS')
if matcha_path not in sys.path:
    sys.path.insert(0, matcha_path)

from cosyvoice.cli.cosyvoice import CosyVoice2

class CosyVoice2Handler:
    def __init__(self, model_dir: str):
        try:
            self.cosyvoice = CosyVoice2(model_dir=model_dir)
            logging.info("CosyVoice2 model loaded successfully")
        except Exception as e:
            logging.error(f"Failed to load CosyVoice2 model: {e}")
            raise

    def inference_zero_shot(self, tts_text, prompt_text, prompt_speech_16k, speed=1.0, stream=False):
        try:
            logging.info(f"Starting zero-shot inference for text: {tts_text[:50]}...")
            result_generator = self.cosyvoice.inference_zero_shot(
                tts_text=tts_text,
                prompt_text=prompt_text,
                prompt_speech_16k=prompt_speech_16k,
                stream=stream,
                speed=speed
            )

            for _, j in enumerate(result_generator):
                logging.info("Zero-shot inference completed successfully")
                return j
            
            # 如果生成器为空，返回None
            logging.warning("Zero-shot inference returned empty result")
            return None

        except Exception as e:
            logging.error(f"Error in zero-shot inference: {e}")
            return None
