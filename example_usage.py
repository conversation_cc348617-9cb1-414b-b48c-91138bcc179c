#!/usr/bin/env python3
"""
示例：展示如何使用修改后的CosyVoice2进行语音合成
"""
import os
import sys
import torchaudio

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 添加Matcha-TTS到Python路径
matcha_path = os.path.join(current_dir, 'third_party', 'Matcha-TTS')
if matcha_path not in sys.path:
    sys.path.insert(0, matcha_path)

from cosyvoice.cli.cosyvoice import CosyVoice2
from cosyvoice.utils.file_utils import load_wav

def main():
    print("=== CosyVoice2 语音合成示例 ===")
    
    # 初始化模型（参考代码中的参数）
    model_dir = 'pretrained_models/CosyVoice2-0.5B'
    if not os.path.exists(model_dir):
        print(f"模型目录不存在: {model_dir}")
        print("请确保已下载CosyVoice2-0.5B模型")
        return
    
    print("正在加载CosyVoice2模型...")
    cosyvoice = CosyVoice2(model_dir, load_jit=False, load_trt=False, load_vllm=False, fp16=False)
    print("模型加载完成")
    
    # 使用load_wav加载prompt音频（参考代码的方式）
    prompt_audio_path = './asset/zero_shot_prompt.wav'
    if not os.path.exists(prompt_audio_path):
        print(f"Prompt音频文件不存在: {prompt_audio_path}")
        return
    
    print(f"正在加载prompt音频: {prompt_audio_path}")
    prompt_speech_16k = load_wav(prompt_audio_path, 16000)
    print(f"Prompt音频加载完成，形状: {prompt_speech_16k.shape}")
    
    # 进行语音合成
    tts_text = '收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。'
    prompt_text = '希望你以后能够做的比我还好呦。'
    
    print("正在进行语音合成...")
    print(f"合成文本: {tts_text}")
    print(f"Prompt文本: {prompt_text}")
    
    # NOTE: 如果要重现官网效果，可以添加 text_frontend=False 参数
    for i, j in enumerate(cosyvoice.inference_zero_shot(tts_text, prompt_text, prompt_speech_16k, stream=False)):
        output_path = f'zero_shot_{i}.wav'
        print(f"保存合成音频: {output_path}")
        
        # 直接保存为WAV格式（参考代码的方式）
        torchaudio.save(output_path, j['tts_speech'], cosyvoice.sample_rate)
        print(f"音频已保存，采样率: {cosyvoice.sample_rate}")
        
        # 显示音频信息
        print(f"合成音频形状: {j['tts_speech'].shape}")
        break  # 只处理第一个结果
    
    print("=== 语音合成完成 ===")

if __name__ == "__main__":
    main()
